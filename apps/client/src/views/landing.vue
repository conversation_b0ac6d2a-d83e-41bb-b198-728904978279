<script setup lang="ts">
import {
  <PERSON>,
  Check,
  Copy,
  Mic,
  Play,
  Rocket,
  Shield,
  TrendingUp,
  Users,
  Volume2,
  Zap,
} from 'lucide-vue-next'
import LandingLayout from '@/layouts/LandingLayout.vue'
</script>

<template>
  <LandingLayout>
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-br from-blue-50 via-white to-purple-50 py-20 lg:py-32">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
          <h1 class="text-4xl lg:text-6xl font-bold text-gray-900 mb-6">
            强大的
            <span class="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
              一站式 AI 服务平台
            </span>
          </h1>
          <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            为开发者提供 LLM、TTS、ASR 等 AI 服务，支持高并发调用。
            透明费用统计，实时使用监控，让您专注于产品创新。
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <RouterLink
              to="/auth/register"
              class="bg-blue-600 text-white px-8 py-3 rounded-lg text-lg font-medium hover:bg-blue-700 transition-colors inline-flex items-center justify-center"
            >
              <Rocket class="w-5 h-5 mr-2" />
              免费开始使用
            </RouterLink>
            <a
              href="#services"
              class="border border-gray-300 text-gray-700 px-8 py-3 rounded-lg text-lg font-medium hover:bg-gray-50 transition-colors inline-flex items-center justify-center"
            >
              <Play class="w-5 h-5 mr-2" />
              了解更多
            </a>
          </div>
        </div>
      </div>
    </section>

    <!-- Services Section -->
    <section
      id="services"
      class="py-20 bg-white"
    >
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            AI 服务能力
          </h2>
          <p class="text-xl text-gray-600 max-w-2xl mx-auto">
            提供三大主流 AI 服务，高性能 API 接口，开箱即用
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <!-- LLM服务 -->
          <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-8 hover:shadow-lg transition-shadow">
            <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mb-6">
              <Brain class="w-6 h-6 text-white" />
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-4">
              大语言模型 (LLM)
            </h3>
            <p class="text-gray-600 mb-6">
              支持 GPT、Claude、DeepSeek 等主流大语言模型，提供统一 API 接口
            </p>
            <ul class="space-y-2 text-sm text-gray-600">
              <li class="flex items-center">
                <Check class="w-4 h-4 text-green-500 mr-2" />
                多模型统一接口
              </li>
              <li class="flex items-center">
                <Check class="w-4 h-4 text-green-500 mr-2" />
                高并发处理
              </li>
              <li class="flex items-center">
                <Check class="w-4 h-4 text-green-500 mr-2" />
                Token 使用统计
              </li>
            </ul>
          </div>

          <!-- TTS服务 -->
          <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-8 hover:shadow-lg transition-shadow">
            <div class="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mb-6">
              <Volume2 class="w-6 h-6 text-white" />
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-4">
              语音合成 (TTS)
            </h3>
            <p class="text-gray-600 mb-6">
              高质量语音合成服务，支持多种音色和语言，实时生成
            </p>
            <ul class="space-y-2 text-sm text-gray-600">
              <li class="flex items-center">
                <Check class="w-4 h-4 text-green-500 mr-2" />
                多音色选择
              </li>
              <li class="flex items-center">
                <Check class="w-4 h-4 text-green-500 mr-2" />
                实时语音生成
              </li>
              <li class="flex items-center">
                <Check class="w-4 h-4 text-green-500 mr-2" />
                使用量统计
              </li>
            </ul>
          </div>

          <!-- ASR服务 -->
          <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-8 hover:shadow-lg transition-shadow">
            <div class="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mb-6">
              <Mic class="w-6 h-6 text-white" />
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-4">
              语音识别 (ASR)
            </h3>
            <p class="text-gray-600 mb-6">
              高精度语音识别服务，支持实时和批量处理，多语言识别
            </p>
            <ul class="space-y-2 text-sm text-gray-600">
              <li class="flex items-center">
                <Check class="w-4 h-4 text-green-500 mr-2" />
                高精度识别
              </li>
              <li class="flex items-center">
                <Check class="w-4 h-4 text-green-500 mr-2" />
                实时批量处理
              </li>
              <li class="flex items-center">
                <Check class="w-4 h-4 text-green-500 mr-2" />
                费用透明统计
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <!-- Pricing Section -->
    <section
      id="pricing"
      class="py-20 bg-gray-50"
    >
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            灵活的服务套餐
          </h2>
          <p class="text-xl text-gray-600 max-w-2xl mx-auto">
            按实际使用量付费，透明费用统计，支持多种套餐选择
          </p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <!-- 基础版 -->
          <div class="bg-white rounded-2xl p-8 shadow-sm border">
            <h3 class="text-xl font-semibold text-gray-900 mb-2">
              开发者版
            </h3>
            <p class="text-gray-600 mb-6">
              适合个人开发者和小型项目
            </p>
            <div class="mb-6">
              <span class="text-3xl font-bold text-gray-900">免费</span>
              <span class="text-gray-600">试用</span>
            </div>
            <ul class="space-y-3 mb-8">
              <li class="flex items-center">
                <Check class="w-4 h-4 text-green-500 mr-3" />
                <span class="text-gray-600">每月 10,000 Token 免费额度</span>
              </li>
              <li class="flex items-center">
                <Check class="w-4 h-4 text-green-500 mr-3" />
                <span class="text-gray-600">完整 API 接口</span>
              </li>
              <li class="flex items-center">
                <Check class="w-4 h-4 text-green-500 mr-3" />
                <span class="text-gray-600">费用使用统计</span>
              </li>
            </ul>
            <RouterLink
              to="/auth/register"
              class="w-full bg-gray-900 text-white py-3 rounded-lg font-medium hover:bg-gray-800 transition-colors text-center block"
            >
              开始使用
            </RouterLink>
          </div>

          <!-- 专业版 -->
          <div class="bg-white rounded-2xl p-8 shadow-lg border-2 border-blue-500 relative">
            <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
              <span class="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                推荐
              </span>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">
              专业版
            </h3>
            <p class="text-gray-600 mb-6">
              适合中小企业和成长型项目
            </p>
            <div class="mb-6">
              <span class="text-3xl font-bold text-gray-900">按量</span>
              <span class="text-gray-600">付费</span>
            </div>
            <ul class="space-y-3 mb-8">
              <li class="flex items-center">
                <Check class="w-4 h-4 text-green-500 mr-3" />
                <span class="text-gray-600">无限制 API 调用</span>
              </li>
              <li class="flex items-center">
                <Check class="w-4 h-4 text-green-500 mr-3" />
                <span class="text-gray-600">高并发支持</span>
              </li>
              <li class="flex items-center">
                <Check class="w-4 h-4 text-green-500 mr-3" />
                <span class="text-gray-600">详细费用分析</span>
              </li>
              <li class="flex items-center">
                <Check class="w-4 h-4 text-green-500 mr-3" />
                <span class="text-gray-600">优先技术支持</span>
              </li>
            </ul>
            <RouterLink
              to="/auth/register"
              class="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors text-center block"
            >
              选择专业版
            </RouterLink>
          </div>

          <!-- 企业版 -->
          <div class="bg-white rounded-2xl p-8 shadow-sm border">
            <h3 class="text-xl font-semibold text-gray-900 mb-2">
              企业版
            </h3>
            <p class="text-gray-600 mb-6">
              适合大型企业和高并发场景
            </p>
            <div class="mb-6">
              <span class="text-3xl font-bold text-gray-900">定制</span>
              <span class="text-gray-600">方案</span>
            </div>
            <ul class="space-y-3 mb-8">
              <li class="flex items-center">
                <Check class="w-4 h-4 text-green-500 mr-3" />
                <span class="text-gray-600">专属服务集群</span>
              </li>
              <li class="flex items-center">
                <Check class="w-4 h-4 text-green-500 mr-3" />
                <span class="text-gray-600">私有化部署</span>
              </li>
              <li class="flex items-center">
                <Check class="w-4 h-4 text-green-500 mr-3" />
                <span class="text-gray-600">定制化接口</span>
              </li>
              <li class="flex items-center">
                <Check class="w-4 h-4 text-green-500 mr-3" />
                <span class="text-gray-600">专属客户经理</span>
              </li>
            </ul>
            <a
              href="#contact"
              class="w-full bg-gray-900 text-white py-3 rounded-lg font-medium hover:bg-gray-800 transition-colors text-center block"
            >
              联系销售
            </a>
          </div>
        </div>
      </div>
    </section>

    <!-- Quick Start Section -->
    <section
      id="docs"
      class="py-20 bg-white"
    >
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            快速接入
          </h2>
          <p class="text-xl text-gray-600 max-w-2xl mx-auto">
            三步即可接入我们的AI服务平台，开始调用各种AI能力
          </p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <!-- 步骤说明 -->
          <div class="space-y-8">
            <div class="flex items-start space-x-4">
              <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-semibold text-sm">
                1
              </div>
              <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">
                  注册账号
                </h3>
                <p class="text-gray-600">
                  使用邮箱快速注册，立即获得免费试用额度
                </p>
              </div>
            </div>

            <div class="flex items-start space-x-4">
              <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-semibold text-sm">
                2
              </div>
              <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">
                  创建应用
                </h3>
                <p class="text-gray-600">
                  在控制台创建应用，获取 API Key 开始调用 AI 服务
                </p>
              </div>
            </div>

            <div class="flex items-start space-x-4">
              <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-semibold text-sm">
                3
              </div>
              <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">
                  开始调用
                </h3>
                <p class="text-gray-600">
                  使用 API Key 调用 LLM/TTS/ASR 服务，实时查看使用统计
                </p>
              </div>
            </div>
          </div>

          <!-- 代码示例 -->
          <div class="bg-gray-900 rounded-lg p-6 text-sm">
            <div class="flex items-center justify-between mb-4">
              <span class="text-gray-400">API 调用示例</span>
              <button class="text-gray-400 hover:text-white transition-colors">
                <Copy class="w-4 h-4" />
              </button>
            </div>
            <pre class="text-green-400 overflow-x-auto"><code>curl -X POST https://api.example.com/v1/llm/chat \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {
        "role": "user",
        "content": "Hello, world!"
      }
    ]
  }'</code></pre>
          </div>
        </div>
      </div>
    </section>

    <!-- About Section -->
    <section
      id="about"
      class="py-20 bg-gray-50"
    >
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            关于我们
          </h2>
          <p class="text-xl text-gray-600 max-w-2xl mx-auto">
            专注于为开发者提供最优质的 AI 服务计费解决方案
          </p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <!-- 公司介绍 -->
          <div class="space-y-6">
            <h3 class="text-2xl font-semibold text-gray-900 mb-4">
              我们的使命
            </h3>
            <p class="text-gray-600 leading-relaxed">
              AI 计费平台致力于简化 AI 服务的计费复杂性，让开发者能够专注于创新而非计费管理。我们提供透明、精确、实时的计费服务，支持 LLM、TTS、ASR 等多种 AI 服务类型。
            </p>
            <p class="text-gray-600 leading-relaxed">
              通过我们的平台，您可以轻松管理 API 密钥、监控使用情况、控制成本，并获得详细的分析报告。我们相信，优秀的计费系统应该是无感知的，让您的业务流程更加顺畅。
            </p>

            <div class="grid grid-cols-2 gap-6 mt-8">
              <div class="text-center">
                <div class="text-3xl font-bold text-blue-600 mb-2">
                  99.9%
                </div>
                <div class="text-gray-600">
                  服务可用性
                </div>
              </div>
              <div class="text-center">
                <div class="text-3xl font-bold text-blue-600 mb-2">
                  1000+
                </div>
                <div class="text-gray-600">
                  开发者信赖
                </div>
              </div>
              <div class="text-center">
                <div class="text-3xl font-bold text-blue-600 mb-2">
                  24/7
                </div>
                <div class="text-gray-600">
                  技术支持
                </div>
              </div>
              <div class="text-center">
                <div class="text-3xl font-bold text-blue-600 mb-2">
                  0.1s
                </div>
                <div class="text-gray-600">
                  响应时间
                </div>
              </div>
            </div>
          </div>

          <!-- 团队特色 -->
          <div class="space-y-6">
            <h3 class="text-2xl font-semibold text-gray-900 mb-4">
              我们的优势
            </h3>

            <div class="space-y-4">
              <div class="flex items-start space-x-4">
                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Shield class="w-4 h-4 text-blue-600" />
                </div>
                <div>
                  <h4 class="font-semibold text-gray-900 mb-1">
                    安全可靠
                  </h4>
                  <p class="text-gray-600 text-sm">
                    企业级安全保障，数据加密传输，符合国际安全标准
                  </p>
                </div>
              </div>

              <div class="flex items-start space-x-4">
                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                  <Zap class="w-4 h-4 text-green-600" />
                </div>
                <div>
                  <h4 class="font-semibold text-gray-900 mb-1">
                    高性能
                  </h4>
                  <p class="text-gray-600 text-sm">
                    毫秒级响应，支持高并发，稳定处理大规模请求
                  </p>
                </div>
              </div>

              <div class="flex items-start space-x-4">
                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Users class="w-4 h-4 text-purple-600" />
                </div>
                <div>
                  <h4 class="font-semibold text-gray-900 mb-1">
                    专业团队
                  </h4>
                  <p class="text-gray-600 text-sm">
                    经验丰富的技术团队，提供专业的技术支持和咨询服务
                  </p>
                </div>
              </div>

              <div class="flex items-start space-x-4">
                <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                  <TrendingUp class="w-4 h-4 text-orange-600" />
                </div>
                <div>
                  <h4 class="font-semibold text-gray-900 mb-1">
                    持续创新
                  </h4>
                  <p class="text-gray-600 text-sm">
                    不断优化产品功能，紧跟AI技术发展趋势
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-gradient-to-r from-blue-600 to-purple-600">
      <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl lg:text-4xl font-bold text-white mb-4">
          准备好开始了吗？
        </h2>
        <p class="text-xl text-blue-100 mb-8">
          立即注册，获得免费试用额度，体验强大的AI服务平台
        </p>
        <RouterLink
          to="/auth/register"
          class="bg-white text-blue-600 px-8 py-3 rounded-lg text-lg font-medium hover:bg-gray-100 transition-colors inline-flex items-center"
        >
          <Zap class="w-5 h-5 mr-2" />
          免费开始使用
        </RouterLink>
      </div>
    </section>
  </LandingLayout>
</template>
