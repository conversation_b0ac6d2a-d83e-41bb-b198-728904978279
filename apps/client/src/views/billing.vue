<script setup lang="ts">
import {
  ArcElement,
  CategoryScale,
  Chart as ChartJS,
  Legend,
  LinearScale,
  LineElement,
  PointElement,
  Title,
  Tooltip,
} from 'chart.js'
import {
  Activity,
  DollarSign,
  Download,
  RefreshCw,
  TrendingUp,
  Zap,
} from 'lucide-vue-next'
import { computed, onMounted, reactive, ref, watch } from 'vue'
import {
  Doughnut,
  Line,
} from 'vue-chartjs'
import { toast } from 'vue-sonner'
import { appApi, billingApi } from '@/api'
import ClientLayout from '@/layouts/ClientLayout.vue'

// 注册Chart.js组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
)

const loading = ref(false)
const timeRange = ref('30d')
const customStartDate = ref('')
const customEndDate = ref('')
const selectedApplication = ref('')
const trendMetric = ref('calls')

// 应用列表
const applications = ref([])

// 统计数据
const billingStats = reactive({
  totalCalls: 0,
  totalTokens: 0,
  totalCost: 0,
  avgCostPerCall: 0,
})

// 趋势数据
const trendData = reactive({
  labels: [],
  datasets: [{ data: [] }],
})

// 服务分布数据
const serviceData = reactive({
  labels: [],
  datasets: [{ data: [] }],
})

// 详细记录
const billingRecords = ref([])
const recordPagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0,
})

// 图表配置
const trendChartData = computed(() => ({
  labels: trendData.labels,
  datasets: [
    {
      label: getTrendLabel(),
      data: trendData.datasets[0]?.data || [],
      borderColor: 'rgb(59, 130, 246)',
      backgroundColor: 'rgba(59, 130, 246, 0.1)',
      tension: 0.4,
    },
  ],
}))

const trendChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false,
    },
  },
  scales: {
    y: {
      beginAtZero: true,
    },
  },
}

const serviceChartData = computed(() => ({
  labels: serviceData.labels,
  datasets: [
    {
      data: serviceData.datasets[0]?.data || [],
      backgroundColor: [
        'rgba(59, 130, 246, 0.8)',
        'rgba(16, 185, 129, 0.8)',
        'rgba(139, 92, 246, 0.8)',
        'rgba(245, 158, 11, 0.8)',
      ],
      borderWidth: 0,
    },
  ],
}))

const serviceChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom' as const,
    },
  },
} as any

// 获取趋势图标签
function getTrendLabel() {
  switch (trendMetric.value) {
    case 'calls':
      return '调用次数'
    case 'tokens':
      return 'Token使用量'
    case 'cost':
      return '费用 (¥)'
    default:
      return ''
  }
}

// 格式化数字
function formatNumber(num: number) {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`
  }
  if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`
  }
  return num.toString()
}

// 格式化货币
function formatCurrency(amount: number) {
  return amount.toFixed(2)
}

// 格式化日期时间
function formatDateTime(dateString: string) {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 获取服务类型名称
function getServiceTypeName(type: string) {
  const names = {
    llm: 'LLM',
    tts: 'TTS',
    asr: 'ASR',
  }
  return names[type] || type
}

// 获取服务类型颜色
function getServiceTypeColor(type: string) {
  const colors = {
    llm: 'bg-blue-100 text-blue-800',
    tts: 'bg-green-100 text-green-800',
    asr: 'bg-purple-100 text-purple-800',
  }
  return colors[type] || 'bg-gray-100 text-gray-800'
}

// 时间范围变化
function onTimeRangeChange() {
  if (timeRange.value !== 'custom') {
    loadBillingData()
  }
}

// 加载应用列表
async function loadApplications() {
  try {
    const data = await appApi.getList({
      page: 1,
      page_size: 10,
    })
    applications.value = Array.isArray(data) ? data : []
  }
  catch (error) {
    console.error('Failed to load applications:', error)
  }
}

// 加载统计数据
async function loadBillingStats() {
  try {
    const data = await billingApi.getBillingStats()
    Object.assign(billingStats, data || {})
  }
  catch (error) {
    console.error('Failed to load billing stats:', error)
  }
}

// 加载趋势数据
async function loadTrendData() {
  try {
    const params = {
      ...getQueryParams(),
      metric: trendMetric.value,
    }
    const data = await billingApi.getUsageTrends(params)
    Object.assign(trendData, data)
  }
  catch (error) {
    console.error('Failed to load trend data:', error)
  }
}

// 加载服务分布数据
async function loadServiceData() {
  try {
    const params = getQueryParams()
    const data = await billingApi.getServiceUsage(params)
    Object.assign(serviceData, data)
  }
  catch (error) {
    console.error('Failed to load service data:', error)
  }
}

// 加载详细记录
async function loadBillingRecords() {
  try {
    const params = {
      ...getQueryParams(),
      page: recordPagination.page,
      pageSize: recordPagination.pageSize,
    }
    const data = await billingApi.getBillingPeriods(params)
    billingRecords.value = Array.isArray(data) ? data : []
    recordPagination.total = Array.isArray(data) ? data.length : 0
  }
  catch (error) {
    console.error('Failed to load billing records:', error)
  }
}

// 获取查询参数
function getQueryParams() {
  const params: any = {}

  if (timeRange.value === 'custom') {
    if (customStartDate.value)
      params.startDate = customStartDate.value
    if (customEndDate.value)
      params.endDate = customEndDate.value
  }
  else {
    params.period = timeRange.value
  }

  if (selectedApplication.value) {
    params.applicationId = selectedApplication.value
  }

  return params
}

// 加载所有计费数据
async function loadBillingData() {
  loading.value = true
  try {
    await Promise.all([
      loadBillingStats(),
      loadTrendData(),
      loadServiceData(),
      loadBillingRecords(),
    ])
  }
  catch (error) {
    toast.error('加载数据失败')
  }
  finally {
    loading.value = false
  }
}

// 刷新数据
function refreshData() {
  loadBillingData()
}

// 导出报告
async function exportReport() {
  try {
    const params = getQueryParams()
    await billingApi.exportBillingReport(params)
    toast.success('报告导出成功')
  }
  catch (error) {
    toast.error('导出失败')
  }
}

// 切换记录页面
function changeRecordPage(page: number) {
  recordPagination.page = page
  loadBillingRecords()
}

// 监听趋势指标变化
watch(trendMetric, () => {
  loadTrendData()
})

// 监听自定义日期变化
watch([customStartDate, customEndDate], () => {
  if (timeRange.value === 'custom' && customStartDate.value && customEndDate.value) {
    loadBillingData()
  }
})

// 初始化
onMounted(() => {
  loadApplications()
  loadBillingData()
})
</script>

<template>
  <ClientLayout>
    <div class="space-y-6">
      <!-- 页面标题 -->
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">
            计费统计
          </h1>
          <p class="text-gray-600">
            查看详细的使用统计和费用分析
          </p>
        </div>
        <div class="flex items-center space-x-3">
          <button
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            @click="exportReport"
          >
            <Download class="w-4 h-4 mr-2" />
            导出报告
          </button>
          <button
            :disabled="loading"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            @click="refreshData"
          >
            <RefreshCw
              class="w-4 h-4 mr-2"
              :class="[{ 'animate-spin': loading }]"
            />
            刷新
          </button>
        </div>
      </div>

      <!-- 时间范围选择 -->
      <div class="bg-white shadow rounded-lg p-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              时间范围
            </label>
            <select
              v-model="timeRange"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              @change="onTimeRangeChange"
            >
              <option value="today">
                今天
              </option>
              <option value="7d">
                最近7天
              </option>
              <option value="30d">
                最近30天
              </option>
              <option value="90d">
                最近90天
              </option>
              <option value="custom">
                自定义
              </option>
            </select>
          </div>
          <div v-if="timeRange === 'custom'">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              开始日期
            </label>
            <input
              v-model="customStartDate"
              type="date"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
          </div>
          <div v-if="timeRange === 'custom'">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              结束日期
            </label>
            <input
              v-model="customEndDate"
              type="date"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              应用筛选
            </label>
            <select
              v-model="selectedApplication"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              @change="loadBillingData"
            >
              <option value="">
                全部应用
              </option>
              <option
                v-for="app in applications"
                :key="app.id"
                :value="app.id"
              >
                {{ app.name }}
              </option>
            </select>
          </div>
        </div>
      </div>

      <!-- 统计概览 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                  <Activity class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    总调用次数
                  </dt>
                  <dd class="text-lg font-medium text-gray-900">
                    {{ formatNumber(billingStats.totalCalls) }}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                  <Zap class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    总Token使用
                  </dt>
                  <dd class="text-lg font-medium text-gray-900">
                    {{ formatNumber(billingStats.totalTokens) }}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                  <DollarSign class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    总费用
                  </dt>
                  <dd class="text-lg font-medium text-gray-900">
                    ¥{{ formatCurrency(billingStats.totalCost) }}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                  <TrendingUp class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    平均单价
                  </dt>
                  <dd class="text-lg font-medium text-gray-900">
                    ¥{{ formatCurrency(billingStats.avgCostPerCall) }}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 使用趋势图 -->
        <div class="bg-white shadow rounded-lg p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900">
              使用趋势
            </h3>
            <div class="flex items-center space-x-2">
              <button
                class="px-3 py-1 text-sm rounded-md"
                :class="[
                  trendMetric === 'calls'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700',
                ]"
                @click="trendMetric = 'calls'"
              >
                调用次数
              </button>
              <button
                class="px-3 py-1 text-sm rounded-md"
                :class="[
                  trendMetric === 'tokens'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700',
                ]"
                @click="trendMetric = 'tokens'"
              >
                Token使用
              </button>
              <button
                class="px-3 py-1 text-sm rounded-md"
                :class="[
                  trendMetric === 'cost'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700',
                ]"
                @click="trendMetric = 'cost'"
              >
                费用
              </button>
            </div>
          </div>
          <div class="h-64">
            <Line
              v-if="trendData.labels.length > 0"
              :data="trendChartData"
              :options="trendChartOptions"
            />
            <div
              v-else
              class="flex items-center justify-center h-full text-gray-500"
            >
              暂无数据
            </div>
          </div>
        </div>

        <!-- 服务分布图 -->
        <div class="bg-white shadow rounded-lg p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900">
              服务使用分布
            </h3>
          </div>
          <div class="h-64">
            <Doughnut
              v-if="serviceData.labels.length > 0"
              :data="serviceChartData"
              :options="serviceChartOptions"
            />
            <div
              v-else
              class="flex items-center justify-center h-full text-gray-500"
            >
              暂无数据
            </div>
          </div>
        </div>
      </div>

      <!-- 详细数据表格 -->
      <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">
            详细记录
          </h3>
        </div>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  时间
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  应用
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  服务类型
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  调用次数
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Token使用
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  费用
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr
                v-for="record in billingRecords"
                :key="record.id"
              >
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ formatDateTime(record.date) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ record.application_name }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    :class="[
                      getServiceTypeColor(record.service_type),
                    ]"
                  >
                    {{ getServiceTypeName(record.service_type) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ formatNumber(record.calls) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ formatNumber(record.tokens) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  ¥{{ formatCurrency(record.cost) }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 分页 -->
        <div
          v-if="recordPagination.total > recordPagination.pageSize"
          class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6"
        >
          <div class="flex-1 flex justify-between sm:hidden">
            <button
              :disabled="recordPagination.page <= 1"
              class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              @click="changeRecordPage(recordPagination.page - 1)"
            >
              上一页
            </button>
            <button
              :disabled="recordPagination.page >= Math.ceil(recordPagination.total / recordPagination.pageSize)"
              class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              @click="changeRecordPage(recordPagination.page + 1)"
            >
              下一页
            </button>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700">
                显示第
                <span class="font-medium">{{ (recordPagination.page - 1) * recordPagination.pageSize + 1 }}</span>
                到
                <span class="font-medium">{{ Math.min(recordPagination.page * recordPagination.pageSize, recordPagination.total) }}</span>
                条，共
                <span class="font-medium">{{ recordPagination.total }}</span>
                条记录
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                <button
                  :disabled="recordPagination.page <= 1"
                  class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  @click="changeRecordPage(recordPagination.page - 1)"
                >
                  上一页
                </button>
                <button
                  :disabled="recordPagination.page >= Math.ceil(recordPagination.total / recordPagination.pageSize)"
                  class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  @click="changeRecordPage(recordPagination.page + 1)"
                >
                  下一页
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ClientLayout>
</template>
