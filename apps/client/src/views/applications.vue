<script setup lang="ts">
import {
  Copy,
  Edit,
  Key,
  Layers,
  Plus,
  RefreshCw,
  RotateCcw,
  Search,
  Trash2,
} from 'lucide-vue-next'
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { toast } from 'vue-sonner'
import { appApi } from '@/api'
import ClientLayout from '@/layouts/ClientLayout.vue'

const loading = ref(false)
const submitting = ref(false)
const searchQuery = ref('')
const statusFilter = ref('')
const sortBy = ref('created_at')

// 模态框状态
const showCreateModal = ref(false)
const showEditModal = ref(false)
const showApiKeysModal = ref(false)

// 应用数据
const applications = ref([])
const selectedApplication = ref(null)
const apiKeys = ref([])

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0,
})

// 应用表单
const applicationForm = reactive({
  name: '',
  comment: '',
})

// 过滤后的应用列表
const filteredApplications = computed(() => {
  let filtered = applications.value

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(
      app =>
        app.name.toLowerCase().includes(query)
        || app.description.toLowerCase().includes(query),
    )
  }

  // 状态过滤
  if (statusFilter.value) {
    filtered = filtered.filter(app => app.status === statusFilter.value)
  }

  // 排序
  filtered.sort((a, b) => {
    if (sortBy.value === 'name') {
      return a.name.localeCompare(b.name)
    }
    if (sortBy.value === 'usage') {
      return (b.stats?.month_calls || 0) - (a.stats?.month_calls || 0)
    }
    return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
  })

  return filtered
})

// 格式化数字
function formatNumber(num: number) {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`
  }
  if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`
  }
  return num.toString()
}

// 格式化货币
function formatCurrency(amount: number) {
  return amount.toFixed(2)
}

// 格式化日期
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 加载应用列表
async function loadApplications() {
  loading.value = true
  try {
    const res = await appApi.getList({
      page: pagination.page,
      page_size: pagination.pageSize,
    })
    applications.value = Array.isArray(res.data) ? res.data : []
    pagination.total = res.pagination.total
  }
  catch (error) {
    toast.error('加载应用列表失败')
  }
  finally {
    loading.value = false
  }
}

// 创建应用
async function submitApplication() {
  submitting.value = true
  try {
    if (showCreateModal.value) {
      await appApi.createApplication(applicationForm)
      toast.success('应用创建成功')
    }
    else {
      await appApi.updateApplication(selectedApplication.value.id, applicationForm)
      toast.success('应用更新成功')
    }
    closeModals()
    loadApplications()
  }
  catch (error) {
    toast.error(showCreateModal.value ? '创建失败' : '更新失败')
  }
  finally {
    submitting.value = false
  }
}

// 编辑应用
function editApplication(app) {
  selectedApplication.value = app
  applicationForm.name = app.name
  applicationForm.comment = app.comment || ''
  showEditModal.value = true
}

// 删除应用
async function deleteApplication(app) {
  if (!confirm(`确定要删除应用 "${app.name}" 吗？此操作不可恢复。`)) {
    return
  }

  try {
    await appApi.deleteApplication(app.id)
    toast.success('应用删除成功')
    loadApplications()
  }
  catch (error) {
    toast.error('删除失败')
  }
}

// 查看API Keys
async function viewApiKeys(app) {
  selectedApplication.value = app
}

// 创建API Key
async function createApiKey() {
  const name = prompt('请输入API Key名称：')
  if (!name)
    return

  try {
    await appApi.createApplication(applicationForm)
    toast.success('API Key创建成功')
    viewApiKeys(selectedApplication.value)
  }
  catch (error) {
    toast.error('创建API Key失败')
  }
}

// 复制API Key
async function copyApiKey(key: string) {
  try {
    await navigator.clipboard.writeText(key)
    toast.success('API Key已复制到剪贴板')
  }
  catch (error) {
    toast.error('复制失败')
  }
}

// 重新生成API Key
async function regenerateApiKey(apiKey) {
  if (!confirm('确定要重新生成此API Key吗？原Key将失效。')) {
    return
  }

  try {
    await appApi.regenerateApiKey(selectedApplication.value.id)
    toast.success('API Key重新生成成功')
    viewApiKeys(selectedApplication.value)
  }
  catch (error) {
    toast.error('重新生成失败')
  }
}

// 删除API Key
async function deleteApiKey(apiKey) {
  if (!confirm(`确定要删除API Key "${apiKey.name}" 吗？`)) {
    return
  }

  try {
    await appApi.deleteApplication(selectedApplication.value.id)
    toast.success('API Key删除成功')
    viewApiKeys(selectedApplication.value)
  }
  catch (error) {
    toast.error('删除失败')
  }
}

// 关闭模态框
function closeModals() {
  showCreateModal.value = false
  showEditModal.value = false
  selectedApplication.value = null
  applicationForm.name = ''
  applicationForm.comment = ''
}

// 切换页面
function changePage(page: number) {
  pagination.page = page
  loadApplications()
}

// 监听搜索和筛选变化
watch([searchQuery, statusFilter, sortBy], () => {
  pagination.page = 1
})

// 初始化
onMounted(() => {
  loadApplications()
})
</script>

<template>
  <ClientLayout>
    <div class="space-y-6">
      <!-- 页面标题 -->
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">
            应用管理
          </h1>
          <p class="text-gray-600">
            管理您的应用和API Keys
          </p>
        </div>
        <div class="flex items-center space-x-3">
          <button
            :disabled="loading"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            @click="loadApplications"
          >
            <RefreshCw
              class="w-4 h-4 mr-2"
              :class="[{ 'animate-spin': loading }]"
            />
            刷新
          </button>
          <button
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            @click="showCreateModal = true"
          >
            <Plus class="w-4 h-4 mr-2" />
            创建应用
          </button>
        </div>
      </div>

      <!-- 搜索和筛选 -->
      <div class="bg-white shadow rounded-lg p-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              搜索应用
            </label>
            <div class="relative">
              <input
                v-model="searchQuery"
                type="text"
                placeholder="输入应用名称或描述"
                class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
              <Search class="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
            </div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              状态筛选
            </label>
            <select
              v-model="statusFilter"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="">
                全部状态
              </option>
              <option value="active">
                活跃
              </option>
              <option value="inactive">
                停用
              </option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              排序方式
            </label>
            <select
              v-model="sortBy"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="created_at">
                创建时间
              </option>
              <option value="name">
                应用名称
              </option>
              <option value="usage">
                使用量
              </option>
            </select>
          </div>
        </div>
      </div>

      <!-- 应用列表 -->
      <div class="bg-white shadow rounded-lg overflow-hidden">
        <div
          v-if="filteredApplications.length === 0 && !loading"
          class="text-center py-12"
        >
          <Layers class="mx-auto h-12 w-12 text-gray-400" />
          <h3 class="mt-2 text-sm font-medium text-gray-900">
            暂无应用
          </h3>
          <p class="mt-1 text-sm text-gray-500">
            创建您的第一个应用开始使用AI服务
          </p>
          <div class="mt-6">
            <button
              class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              @click="showCreateModal = true"
            >
              <Plus class="w-4 h-4 mr-2" />
              创建应用
            </button>
          </div>
        </div>

        <div
          v-else
          class="divide-y divide-gray-200"
        >
          <div
            v-for="app in filteredApplications"
            :key="app.id"
            class="p-6 hover:bg-gray-50"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Layers class="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <h3 class="text-lg font-medium text-gray-900">
                    {{ app.name }}
                  </h3>
                  <p class="text-sm text-gray-500">
                    {{ app.description }}
                  </p>
                  <div class="flex items-center space-x-4 mt-2">
                    <span
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                      :class="[
                        app.status === 'active'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800',
                      ]"
                    >
                      {{ app.status === 'active' ? '活跃' : '停用' }}
                    </span>
                    <span class="text-sm text-gray-500">
                      创建于 {{ formatDate(app.created_at) }}
                    </span>
                  </div>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <button
                  class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                  @click="viewApiKeys(app)"
                >
                  <Key class="w-4 h-4 mr-2" />
                  API Keys
                </button>
                <button
                  class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                  @click="editApplication(app)"
                >
                  <Edit class="w-4 h-4 mr-2" />
                  编辑
                </button>
                <button
                  class="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50"
                  @click="deleteApplication(app)"
                >
                  <Trash2 class="w-4 h-4 mr-2" />
                  删除
                </button>
              </div>
            </div>

            <!-- 使用统计 -->
            <div class="mt-4 grid grid-cols-1 md:grid-cols-4 gap-4">
              <div class="bg-gray-50 rounded-lg p-3">
                <div class="text-sm font-medium text-gray-500">
                  今日调用
                </div>
                <div class="text-lg font-semibold text-gray-900">
                  {{ formatNumber(app.stats?.today_calls || 0) }}
                </div>
              </div>
              <div class="bg-gray-50 rounded-lg p-3">
                <div class="text-sm font-medium text-gray-500">
                  本月调用
                </div>
                <div class="text-lg font-semibold text-gray-900">
                  {{ formatNumber(app.stats?.month_calls || 0) }}
                </div>
              </div>
              <div class="bg-gray-50 rounded-lg p-3">
                <div class="text-sm font-medium text-gray-500">
                  今日费用
                </div>
                <div class="text-lg font-semibold text-gray-900">
                  ¥{{ formatCurrency(app.stats?.today_cost || 0) }}
                </div>
              </div>
              <div class="bg-gray-50 rounded-lg p-3">
                <div class="text-sm font-medium text-gray-500">
                  本月费用
                </div>
                <div class="text-lg font-semibold text-gray-900">
                  ¥{{ formatCurrency(app.stats?.month_cost || 0) }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div
        v-if="pagination.total > pagination.pageSize"
        class="flex items-center justify-between"
      >
        <div class="text-sm text-gray-700">
          显示第 {{ (pagination.page - 1) * pagination.pageSize + 1 }} 到
          {{ Math.min(pagination.page * pagination.pageSize, pagination.total) }} 条，
          共 {{ pagination.total }} 条记录
        </div>
        <div class="flex items-center space-x-2">
          <button
            :disabled="pagination.page <= 1"
            class="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            @click="changePage(pagination.page - 1)"
          >
            上一页
          </button>
          <button
            :disabled="pagination.page >= Math.ceil(pagination.total / pagination.pageSize)"
            class="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            @click="changePage(pagination.page + 1)"
          >
            下一页
          </button>
        </div>
      </div>
    </div>

    <!-- 创建/编辑应用模态框 -->
    <div
      v-if="showCreateModal || showEditModal"
      class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
      @click="closeModals"
    >
      <div
        class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"
        @click.stop
      >
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">
            {{ showCreateModal ? '创建应用' : '编辑应用' }}
          </h3>
          <form
            class="space-y-4"
            @submit.prevent="submitApplication"
          >
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                应用名称
              </label>
              <input
                v-model="applicationForm.name"
                type="text"
                required
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="输入应用名称"
              >
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                应用备注
              </label>
              <textarea
                v-model="applicationForm.comment"
                rows="3"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="输入应用备注（可选）"
              />
            </div>
            <div class="flex items-center justify-end space-x-3 pt-4">
              <button
                type="button"
                class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                @click="closeModals"
              >
                取消
              </button>
              <button
                type="submit"
                :disabled="submitting"
                class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
              >
                {{ submitting ? '保存中...' : '保存' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- API Keys模态框 -->
    <div
      v-if="showApiKeysModal"
      class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
      @click="showApiKeysModal = false"
    >
      <div
        class="relative top-10 mx-auto p-5 border w-4/5 max-w-4xl shadow-lg rounded-md bg-white"
        @click.stop
      >
        <div class="mt-3">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900">
              {{ selectedApplication?.name }} - API Keys
            </h3>
            <button
              class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              @click="createApiKey"
            >
              <Plus class="w-4 h-4 mr-2" />
              创建API Key
            </button>
          </div>

          <div class="space-y-3">
            <div
              v-for="apiKey in apiKeys"
              :key="apiKey.id"
              class="border border-gray-200 rounded-lg p-4"
            >
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="font-medium text-gray-900">
                    {{ apiKey.name }}
                  </h4>
                  <p class="text-sm text-gray-500">
                    {{ apiKey.description }}
                  </p>
                  <div class="flex items-center space-x-4 mt-2">
                    <span
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                      :class="[
                        apiKey.status === 'active'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800',
                      ]"
                    >
                      {{ apiKey.status === 'active' ? '活跃' : '停用' }}
                    </span>
                    <span class="text-sm text-gray-500">
                      创建于 {{ formatDate(apiKey.created_at) }}
                    </span>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <button
                    class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                    @click="copyApiKey(apiKey.key)"
                  >
                    <Copy class="w-4 h-4 mr-2" />
                    复制
                  </button>
                  <button
                    class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                    @click="regenerateApiKey(apiKey)"
                  >
                    <RotateCcw class="w-4 h-4 mr-2" />
                    重新生成
                  </button>
                  <button
                    class="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50"
                    @click="deleteApiKey(apiKey)"
                  >
                    <Trash2 class="w-4 h-4 mr-2" />
                    删除
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ClientLayout>
</template>
